"""
3D Visualization and Export utilities for BEV maps
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import os
from typing import List, Dict, Tuple
import json


def bev_to_3d_points(bev_map: np.ndarray, grid_conf: Dict, threshold: float = 0.5) -> np.ndarray:
    """
    Convert BEV occupancy map to 3D point cloud
    
    Args:
        bev_map: 2D occupancy map from model output
        grid_conf: Grid configuration with bounds
        threshold: Occupancy threshold for point generation
        
    Returns:
        Array of 3D points (N, 3) where each row is [x, y, z]
    """
    # Get grid bounds
    x_min, x_max, x_res = grid_conf['xbound']
    y_min, y_max, y_res = grid_conf['ybound']
    z_min, z_max, z_res = grid_conf['zbound']
    
    # Find occupied cells
    occupied = bev_map > threshold
    y_indices, x_indices = np.where(occupied)
    
    # Convert indices to real-world coordinates
    x_coords = x_min + (x_indices + 0.5) * x_res
    y_coords = y_min + (y_indices + 0.5) * y_res
    
    # For road surface, use ground level (z=0) with some variation based on occupancy
    z_coords = np.zeros_like(x_coords)
    
    # Add height variation based on occupancy probability
    occupancy_values = bev_map[y_indices, x_indices]
    z_coords += (occupancy_values - 0.5) * 0.2  # Small height variation
    
    # Stack coordinates
    points_3d = np.column_stack([x_coords, y_coords, z_coords])
    
    return points_3d


def create_road_mesh(bev_maps: List[np.ndarray], grid_conf: Dict, 
                    threshold: float = 0.5) -> Tuple[np.ndarray, np.ndarray]:
    """
    Create a 3D mesh from multiple BEV maps (temporal sequence)
    
    Args:
        bev_maps: List of BEV occupancy maps
        grid_conf: Grid configuration
        threshold: Occupancy threshold
        
    Returns:
        Tuple of (vertices, faces) for 3D mesh
    """
    all_points = []
    
    # Process each BEV map
    for i, bev_map in enumerate(bev_maps):
        points = bev_to_3d_points(bev_map, grid_conf, threshold)
        
        # Add temporal dimension (y-offset for sequence visualization)
        if len(bev_maps) > 1:
            time_offset = i * 2.0  # 2 meter spacing between time steps
            points[:, 1] += time_offset
        
        all_points.append(points)
    
    # Combine all points
    if all_points:
        vertices = np.vstack(all_points)
    else:
        vertices = np.empty((0, 3))
    
    # For simplicity, we'll create a point cloud rather than a complex mesh
    # In a full implementation, you might use Delaunay triangulation or marching cubes
    faces = np.empty((0, 3), dtype=int)  # Empty faces array
    
    return vertices, faces


def export_to_ply(vertices: np.ndarray, faces: np.ndarray, filepath: str, 
                 colors: np.ndarray = None):
    """
    Export 3D data to PLY format
    
    Args:
        vertices: Array of 3D vertices (N, 3)
        faces: Array of face indices (M, 3)
        filepath: Output file path
        colors: Optional vertex colors (N, 3) in range [0, 255]
    """
    with open(filepath, 'w') as f:
        # Write PLY header
        f.write("ply\n")
        f.write("format ascii 1.0\n")
        f.write(f"element vertex {len(vertices)}\n")
        f.write("property float x\n")
        f.write("property float y\n")
        f.write("property float z\n")
        
        if colors is not None:
            f.write("property uchar red\n")
            f.write("property uchar green\n")
            f.write("property uchar blue\n")
        
        if len(faces) > 0:
            f.write(f"element face {len(faces)}\n")
            f.write("property list uchar int vertex_indices\n")
        
        f.write("end_header\n")
        
        # Write vertices
        for i, vertex in enumerate(vertices):
            if colors is not None:
                color = colors[i].astype(int)
                f.write(f"{vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f} {color[0]} {color[1]} {color[2]}\n")
            else:
                f.write(f"{vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f}\n")
        
        # Write faces
        for face in faces:
            f.write(f"3 {face[0]} {face[1]} {face[2]}\n")
    
    print(f"✓ Exported PLY file: {filepath}")


def export_to_obj(vertices: np.ndarray, faces: np.ndarray, filepath: str):
    """
    Export 3D data to OBJ format
    
    Args:
        vertices: Array of 3D vertices (N, 3)
        faces: Array of face indices (M, 3)
        filepath: Output file path
    """
    with open(filepath, 'w') as f:
        f.write("# 3D Road Model from Dashcam\n")
        f.write("# Generated by Lift-Splat-Shoot\n\n")
        
        # Write vertices
        for vertex in vertices:
            f.write(f"v {vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f}\n")
        
        # Write faces (OBJ uses 1-based indexing)
        for face in faces:
            f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
    
    print(f"✓ Exported OBJ file: {filepath}")


def visualize_3d_road(vertices: np.ndarray, title: str = "3D Road Reconstruction", 
                     save_path: str = None):
    """
    Create 3D visualization of road points
    
    Args:
        vertices: Array of 3D vertices (N, 3)
        title: Plot title
        save_path: Optional path to save the plot
    """
    if len(vertices) == 0:
        print("No vertices to visualize")
        return
    
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # Create scatter plot
    scatter = ax.scatter(vertices[:, 0], vertices[:, 1], vertices[:, 2], 
                        c=vertices[:, 2], cmap='viridis', s=1, alpha=0.6)
    
    ax.set_xlabel('X (meters)')
    ax.set_ylabel('Y (meters)')
    ax.set_zlabel('Z (meters)')
    ax.set_title(title)
    
    # Add colorbar
    plt.colorbar(scatter, ax=ax, label='Height (meters)')
    
    # Set equal aspect ratio
    max_range = np.array([vertices[:, 0].max() - vertices[:, 0].min(),
                         vertices[:, 1].max() - vertices[:, 1].min(),
                         vertices[:, 2].max() - vertices[:, 2].min()]).max() / 2.0
    
    mid_x = (vertices[:, 0].max() + vertices[:, 0].min()) * 0.5
    mid_y = (vertices[:, 1].max() + vertices[:, 1].min()) * 0.5
    mid_z = (vertices[:, 2].max() + vertices[:, 2].min()) * 0.5
    
    ax.set_xlim(mid_x - max_range, mid_x + max_range)
    ax.set_ylim(mid_y - max_range, mid_y + max_range)
    ax.set_zlim(mid_z - max_range, mid_z + max_range)
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"✓ 3D visualization saved: {save_path}")
    
    plt.show()


def create_road_model_from_results(results: List[Dict], grid_conf: Dict, 
                                  output_dir: str, threshold: float = 0.5):
    """
    Create 3D road model from processing results
    
    Args:
        results: List of processing results with BEV maps
        grid_conf: Grid configuration
        output_dir: Output directory for 3D files
        threshold: Occupancy threshold for 3D reconstruction
    """
    print("Creating 3D road model...")
    
    # Extract BEV maps
    bev_maps = [result['bev_output'] for result in results]
    
    # Create 3D mesh
    vertices, faces = create_road_mesh(bev_maps, grid_conf, threshold)
    
    if len(vertices) == 0:
        print("Warning: No 3D points generated. Try lowering the threshold.")
        return
    
    print(f"Generated {len(vertices)} 3D points")
    
    # Create colors based on height
    if len(vertices) > 0:
        z_min, z_max = vertices[:, 2].min(), vertices[:, 2].max()
        if z_max > z_min:
            normalized_z = (vertices[:, 2] - z_min) / (z_max - z_min)
        else:
            normalized_z = np.zeros(len(vertices))
        
        # Create color map (blue to red based on height)
        colors = np.zeros((len(vertices), 3))
        colors[:, 0] = normalized_z * 255  # Red channel
        colors[:, 2] = (1 - normalized_z) * 255  # Blue channel
        colors[:, 1] = 128  # Green channel (constant)
    else:
        colors = None
    
    # Export to different formats
    ply_path = os.path.join(output_dir, "road_model.ply")
    obj_path = os.path.join(output_dir, "road_model.obj")
    
    export_to_ply(vertices, faces, ply_path, colors)
    export_to_obj(vertices, faces, obj_path)
    
    # Create 3D visualization
    viz_path = os.path.join(output_dir, "3d_visualization.png")
    visualize_3d_road(vertices, "3D Road Reconstruction from Dashcam", viz_path)
    
    # Save metadata
    metadata = {
        'num_vertices': len(vertices),
        'num_faces': len(faces),
        'bounds': {
            'x': [float(vertices[:, 0].min()), float(vertices[:, 0].max())],
            'y': [float(vertices[:, 1].min()), float(vertices[:, 1].max())],
            'z': [float(vertices[:, 2].min()), float(vertices[:, 2].max())]
        },
        'threshold_used': threshold,
        'grid_config': grid_conf
    }
    
    metadata_path = os.path.join(output_dir, "3d_model_metadata.json")
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"✓ 3D model creation complete!")
    print(f"  - PLY file: {ply_path}")
    print(f"  - OBJ file: {obj_path}")
    print(f"  - Visualization: {viz_path}")
    print(f"  - Metadata: {metadata_path}")
    
    return vertices, faces
